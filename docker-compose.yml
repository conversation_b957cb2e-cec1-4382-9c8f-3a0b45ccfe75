services:
  currency-api:
    container_name: currency-api
    build:
      context: .
      dockerfile: Dockerfile
      target: dev
      args:
        - GITHUB_ACCESS_TOKEN

    volumes:
      - .:/app
    # command: python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --reload-dir app
    ports:
      - 8000:8000
    env_file:
      - .env

  currency-grpc:
    container_name: currency-grpc
    build:
      context: .
      dockerfile: Dockerfile
      target: dev
      args:
        - GITHUB_ACCESS_TOKEN
    command: |
      uv run app/grpc_api.py
    volumes:
      - .:/app
    ports:
      - 50051:50051
    env_file:
      - .env
