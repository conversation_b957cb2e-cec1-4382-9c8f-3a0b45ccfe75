from app.data.currency import CURRENCY_DATA
from app.enums.currency import CurrencyCode


class TestCurrencyCodeEnum:
    def test_enum_contains_all_currency_codes(self):
        expected_codes = {code for code, _ in CURRENCY_DATA}
        enum_codes = {item.name for item in CurrencyCode}

        assert expected_codes == enum_codes

    def test_enum_value_matches_name(self):
        for code in CurrencyCode:
            assert code.value == code.name
