from app.models.currency import Currency, ListCurrenciesResponse


class TestCurrencyModel:
    def test_currency_instantiation(self):
        currency = Currency(
            iso_code="USD", name="United States Dollar", scale=2, symbol="$"
        )

        assert currency.iso_code == "USD"
        assert currency.name == "United States Dollar"
        assert currency.scale == 2
        assert currency.symbol == "$"

    def test_currency_validation_error(self):
        try:
            Currency(iso_code="USD", name="US Dollar", scale="not-a-number", symbol="$")
        except Exception as e:
            assert "Input should be a valid integer" in str(e)

    def test_list_response_instantiation(self):
        currencies = [
            Currency(iso_code="USD", name="US Dollar", scale=2, symbol="$"),
            Currency(iso_code="EUR", name="Euro", scale=2, symbol="€"),
        ]

        response = ListCurrenciesResponse(currencies=currencies)

        assert isinstance(response.currencies, list)
        assert response.currencies[0].iso_code == "USD"
        assert response.currencies[1].symbol == "€"

    def test_empty_currencies_list(self):
        response = ListCurrenciesResponse(currencies=[])
        assert response.currencies == []
