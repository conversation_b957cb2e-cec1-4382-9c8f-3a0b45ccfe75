from datetime import datetime, timedelta, timezone

import jwt

issued_at = datetime.now(tz=timezone.utc)
expired_at = datetime.now(tz=timezone.utc) - timedelta(minutes=1440)

PROPERTY_ID = "22425"
SUPER_ADMIN_TOKEN = jwt.encode(
    dict(
        firstName="Super",
        sub="<EMAIL>",
        associationIds=[],
        propertyIds=["123", "79", "6", PROPERTY_ID],
        island=1,
        mfdUserId=1,
        iat=issued_at,
        exp=expired_at,
        isSuperAdmin=True,
    ),
    "token",
    "HS256",
)
USER_TOKEN = jwt.encode(
    dict(
        firstName="User",
        sub="<EMAIL>",
        associationIds=[],
        propertyIds=["123", "79", "6", PROPERTY_ID],
        island=1,
        mfdUserId=1,
        iat=issued_at,
        exp=expired_at,
        isSuperAdmin=False,
    ),
    "token",
    "HS256",
)
INVALID_TOKEN = jwt.encode(
    dict(
        firstName="Super",
        sub="<EMAIL>",
        associationIds=[],
        propertyIds=[],
        mfdUserId=1,
        iat=issued_at,
        exp=expired_at,
    ),
    "token",
    "HS256",
)
