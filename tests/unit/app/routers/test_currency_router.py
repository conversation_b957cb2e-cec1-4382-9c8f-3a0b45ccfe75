from app.common.constants import BASE_URL, X_PROPERTY_ID
from app.data.currency import CURRENCIES
from tests.unit.fixtures.access_token import (
    INVALID_TOKEN,
    PROPERTY_ID,
    SUPER_ADMIN_TOKEN,
    USER_TOKEN,
)


class TestCurrencyRouter:
    def test_get_currencies_default(self, test_client):
        response = test_client.get(
            f"{BASE_URL}/currencies",
            headers={
                "Authorization": f"Bearer {SUPER_ADMIN_TOKEN}",
                X_PROPERTY_ID: PROPERTY_ID,
            },
        )
        assert response.status_code == 200

        data = response.json()
        assert isinstance(data, list)
        assert len(data) <= 100
        assert data[0]["iso_code"] == CURRENCIES[0].iso_code

    def test_get_currencies_with_limit_and_offset(self, test_client):
        response = test_client.get(
            f"{BASE_URL}/currencies?limit=5&offset=2",
            headers={
                "Authorization": f"Bearer {USER_TOKEN}",
                X_PROPERTY_ID: PROPERTY_ID,
            },
        )
        assert response.status_code == 200

        data = response.json()
        assert len(data) == 5
        assert data[0]["iso_code"] == CURRENCIES[2].iso_code

    def test_get_currencies_offset_beyond_range(self, test_client):
        response = test_client.get(
            f"{BASE_URL}/currencies?offset=9999",
            headers={
                "Authorization": f"Bearer {SUPER_ADMIN_TOKEN}",
                X_PROPERTY_ID: PROPERTY_ID,
            },
        )
        assert response.status_code == 200
        assert response.json() == []

    def test_get_currencies_missing_token(self, test_client):
        response = test_client.get(
            f"{BASE_URL}/currencies", headers={X_PROPERTY_ID: PROPERTY_ID}
        )
        assert response.status_code == 401

    def test_get_currencies_invalid_token(self, test_client):
        response = test_client.get(
            f"{BASE_URL}/currencies",
            headers={
                "Authorization": f"Bearer {INVALID_TOKEN}",
                X_PROPERTY_ID: PROPERTY_ID,
            },
        )
        assert response.status_code in (401, 403)
