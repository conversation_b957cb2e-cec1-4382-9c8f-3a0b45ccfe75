name: 'Unit Tests'
description: Action to run the full suite of unit tests

runs:
  using: composite
  steps:

    - name: Install uv
      uses: astral-sh/setup-uv@v5

    - name: Set up Python
      shell: bash
      run: uv python install

    - name: Get GH app token
      id: gh-app-token
      uses: cloudbeds/composite-actions/gh-app-token@v2

    - name: Git credentials
      shell: bash
      run: |
        git config --global url.'https://x-access-token:${{ steps.gh-app-token.outputs.github-token }}@github.com/'.insteadOf 'https://github.com/'

    - name: Install the project
      shell: bash
      run: uv sync --no-install-project --all-extras --dev
      env:
        GITHUB_ACCESS_TOKEN: ${{ steps.gh-app-token.outputs.github-token }}

    - name: Test with pytest
      shell: bash
      run: |
        make unit-test
