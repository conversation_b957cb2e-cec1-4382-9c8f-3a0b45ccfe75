import asyncio

import grpc
from cloudbeds.currency.v1 import currency_pb2, currency_pb2_grpc
from ddtrace import auto as auto
from ddtrace import tracer
from grpc_health.v1 import health_pb2, health_pb2_grpc
from grpc_health.v1._async import HealthServicer
from grpc_interceptor import AsyncExceptionToStatusInterceptor
from grpc_reflection.v1alpha import reflection

from app.common.logger import GrpcJSONFormatter, configure_logger, logger
from app.common.logger_interceptor import LoggerInterceptor
from app.ddtrace.filters import FilterRequestsOnGrpcMethod
from app.servicer.currency import CurrencyServiceServicer

_cleanup_coroutines = []


async def grpc_api() -> None:
    tracer.configure(
        trace_processors=[FilterRequestsOnGrpcMethod(["/grpc.health.v1.Health/Check"])]
    )
    configure_logger(GrpcJSONFormatter())
    server = grpc.aio.server(
        interceptors=(
            LoggerInterceptor(),
            AsyncExceptionToStatusInterceptor(),
        ),
    )

    currency_pb2_grpc.add_CurrencyServiceServicer_to_server(
        CurrencyServiceServicer(), server
    )
    health_pb2_grpc.add_HealthServicer_to_server(HealthServicer(), server)
    SERVICE_NAMES = (
        currency_pb2.DESCRIPTOR.services_by_name["CurrencyService"].full_name,
        health_pb2.DESCRIPTOR.services_by_name["Health"].full_name,
        reflection.SERVICE_NAME,
    )
    reflection.enable_server_reflection(SERVICE_NAMES, server)

    port = "[::]:50051"
    server.add_insecure_port(port)
    logger.info(f"gRPC server started on port {port}")
    await server.start()

    async def server_graceful_shutdown():
        logger.info("Starting graceful shutdown...")
        await server.stop(5)

    logger.info("Waiting for term")
    _cleanup_coroutines.append(server_graceful_shutdown())
    await server.wait_for_termination()


if __name__ == "__main__":
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(grpc_api())
    finally:
        # loop.run_until_complete(*_cleanup_coroutines)
        loop.close()
