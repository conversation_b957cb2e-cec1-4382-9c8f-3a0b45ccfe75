import types
import uuid

import grpc

import app.common.context_variables as global_vars
from app.common.logger import logger


class LoggerInterceptor(grpc.aio.ServerInterceptor):
    async def intercept_service(self, continuation, handler_call_details):
        # Example HandlerCallDetails object:
        #     _HandlerCallDetails(
        #       method=u'/helloworld.Greeter/SayHello',
        #       invocation_metadata=...)
        metadata = handler_call_details.invocation_metadata
        initial_g = types.SimpleNamespace()
        initial_g.request_id = str(uuid.uuid4())

        for key, value in metadata:
            if key == "user-agent":
                initial_g.user_agent = value
                continue

        initial_g.method = handler_call_details.method
        global_vars.request_global.set(initial_g)

        try:
            return await continuation(handler_call_details)
        except Exception:
            logger.exception("error")
            raise
