# See `Dockerfile` for details.
ARG PYTHON_VERSION=3.12
FROM ghcr.io/astral-sh/uv:0.5.27-python${PYTHON_VERSION}-bookworm-slim AS base

ARG GITHUB_ACCESS_TOKEN

ENV UV_LINK_MODE=copy
ENV UV_CACHE_DIR=/opt/uv-cache/

# Disable Python downloads, because we want to use the system interpreter
# across both images. If using a managed Python version, it needs to be
# copied from the build image into the final image; see `standalone.Dockerfile`
# for an example.
ENV UV_PYTHON_DOWNLOADS=0


RUN apt-get update && apt-get -y install git

WORKDIR /app
COPY . /app

RUN git config --global url."https://x-access-token:${GITHUB_ACCESS_TOKEN}@github.com/".insteadOf "https://github.com/"
RUN uv sync --frozen --no-install-project --no-dev

# Place executables in the environment at the front of the path
ENV PATH="/app/.venv/bin:$PATH"

FROM base AS dev

WORKDIR /app

COPY ./tests ./tests

RUN uv sync --locked

WORKDIR /app

CMD [ "uv", "run", "uvicorn", "app.main:app","--host","0.0.0.0","--port", "8000", "--reload"]

FROM base AS prod

RUN uv sync --no-dev --locked --compile-bytecode

WORKDIR /app

CMD [ "uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000" ]
