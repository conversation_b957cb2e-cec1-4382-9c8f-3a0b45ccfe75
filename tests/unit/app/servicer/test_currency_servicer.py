from unittest.mock import MagicMock, patch

import pytest
from cloudbeds.currency.v1 import currency_pb2

from app.models.currency import Currency
from app.servicer.currency import CurrencyServiceServicer


class TestCurrencyServiceServicer:
    @pytest.mark.asyncio
    async def test_list_currencies_returns_expected_protobuf_response(self):
        mock_request = MagicMock()
        mock_request.limit = 2
        mock_request.offset = 0
        mock_context = MagicMock()

        mock_currencies = [
            Currency(iso_code="USD", name="US Dollar", scale=2, symbol="$"),
            Currency(iso_code="EUR", name="Euro", scale=2, symbol="€"),
        ]

        with patch("app.servicer.currency.CurrencyService") as MockCurrencyService:
            MockCurrencyService.return_value.list_currencies.return_value = (
                mock_currencies
            )

            servicer = CurrencyServiceServicer()

            response = await servicer.ListCurrencies(mock_request, mock_context)

            assert isinstance(response, currency_pb2.ListCurrenciesResponse)
            assert len(response.currencies) == 2

            usd = response.currencies[0]
            assert usd.iso_code == "USD"
            assert usd.name == "US Dollar"
            assert usd.scale == 2
            assert usd.symbol == "$"

            eur = response.currencies[1]
            assert eur.iso_code == "EUR"
            assert eur.name == "Euro"
            assert eur.scale == 2
            assert eur.symbol == "€"
