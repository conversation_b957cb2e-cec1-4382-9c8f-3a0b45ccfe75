import os
from datetime import datetime
from pathlib import Path
from typing import List

import aiohttp
from confluent_kafka import KafkaError, KafkaException, Producer
from confluent_kafka.schema_registry import SchemaRegistryClient
from confluent_kafka.schema_registry.avro import AvroSerializer
from confluent_kafka.serialization import MessageField, SerializationContext

from app.common.logger import logger
from app.data.currency import CURRENCY_DATA


def error_cb(err):
    logger.error(f"[Kafka error] {err}")
    if err.code() in (KafkaError._ALL_BROKERS_DOWN, KafkaError._AUTHENTICATION):
        raise KafkaException(err)


def delivery_report(err, msg):
    if err is not None:
        logger.error(f"[Delivery failed] {err} | Topic: {msg.topic()} Key: {msg.key()}")
    else:
        logger.info(
            f"[Delivered] {msg.key()} to {msg.topic()} [{msg.partition()}] offset {msg.offset()}"
        )


class ExchangeRatesService:
    def __init__(self):
        self.api_key = os.getenv("OPEN_EXCHANGE_RATES_APP_ID")
        self.kafka_topic = os.getenv(
            "KAFKA_TOPIC_EXCHANGE_RATES", "service.currency.exchange_rates"
        )
        self.schema_registry_url = os.getenv("SCHEMA_REGISTRY_URL")
        self.schema_registry_username = os.getenv("SCHEMA_REGISTRY_USERNAME")
        self.schema_registry_password = os.getenv("SCHEMA_REGISTRY_PASSWORD")

        self.kafka_config = {
            "bootstrap.servers": os.getenv("KAFKA_BROKER_URL"),
            "sasl.mechanism": "PLAIN",
            "security.protocol": "SASL_SSL",
            "sasl.username": os.getenv("KAFKA_BROKER_USERNAME"),
            "sasl.password": os.getenv("KAFKA_BROKER_PASSWORD"),
            "error_cb": error_cb,
            "delivery.timeout.ms": 6000000,
            "socket.keepalive.enable": True,
            "retries": 10,
            "retry.backoff.ms": 500,
        }

        self.api_url_template = (
            "https://openexchangerates.org/api/historical/{target_date}.json"
            "?app_id={api_key}&base={base_currency}"
        )

        self.__load_schemas()

    def __load_schemas(self):
        schema_path = Path(__file__).resolve().parent.parent / "schemas"
        with open(schema_path / "exchange_rate-key.avsc") as f:
            self.key_schema = f.read()
        with open(schema_path / "exchange_rate-value.avsc") as f:
            self.value_schema = f.read()

        self.schema_registry = SchemaRegistryClient(
            {
                "url": self.schema_registry_url,
                "basic.auth.user.info": f"{self.schema_registry_username}:{self.schema_registry_password}",
            }
        )

        self.key_serializer = AvroSerializer(
            self.schema_registry, self.key_schema, self.key_to_dict
        )
        self.value_serializer = AvroSerializer(
            self.schema_registry, self.value_schema, self.value_to_dict
        )

    @staticmethod
    def key_to_dict(object, context):
        return {
            "base_currency": object["base_currency"],
            "converted_currency": object["converted_currency"],
            "date": object["date"],
        }

    @staticmethod
    def value_to_dict(object, context):
        return object

    def get_all_base_currencies(self) -> List[str]:
        return [code for code, _ in CURRENCY_DATA]

    async def fetch_rates(
        self, session: aiohttp.ClientSession, base_currency: str, target_date: str
    ) -> dict:
        url = self.api_url_template.format(
            api_key=self.api_key,
            base_currency=base_currency,
            target_date=target_date,
        )

        async with session.get(url) as response:
            if response.status != 200:
                body = await response.text()
                raise Exception(f"[{base_currency}] Failed: {response.status} — {body}")
            return await response.json()

    async def run(self, target_date: str):
        logger.info(f"Fetching exchange rates for {target_date}")
        skipped_bases: List[str] = []

        try:
            producer = Producer(self.kafka_config)
        except KafkaException as e:
            logger.error(f"Failed to create Kafka producer: {e}")
            raise

        try:
            async with aiohttp.ClientSession() as session:
                for base_currency in self.get_all_base_currencies():
                    logger.info(f"Processing base currency: {base_currency}")
                    try:
                        response = await self.fetch_rates(
                            session, base_currency, target_date
                        )
                        rates = response.get("rates", {})
                        timestamp = datetime.utcnow().isoformat()

                        for converted_currency, rate in rates.items():
                            if converted_currency == base_currency:
                                continue

                            key = {
                                "base_currency": base_currency,
                                "converted_currency": converted_currency,
                                "date": target_date,
                            }

                            value = {
                                **key,
                                "conversion_rate": rate,
                                "source": "openexchangerates",
                                "retrieved_at": timestamp,
                            }

                            producer.produce(
                                topic=self.kafka_topic,
                                key=self.key_serializer(
                                    key,
                                    SerializationContext(
                                        self.kafka_topic, MessageField.KEY
                                    ),
                                ),
                                value=self.value_serializer(
                                    value,
                                    SerializationContext(
                                        self.kafka_topic, MessageField.VALUE
                                    ),
                                ),
                                on_delivery=delivery_report,
                            )

                        producer.poll(0.0)
                        logger.info(f"{base_currency} published")

                    except Exception as e:
                        logger.warning(f"Skipped {base_currency}: {e}")
                        skipped_bases.append(base_currency)
        finally:
            logger.info("Flushing Kafka producer...")
            try:
                producer.flush()
            except KafkaException as e:
                logger.error(f"Failed to flush Kafka producer: {e}")
                raise

            logger.info("Done flushing Kafka producer.")

            if skipped_bases:
                logger.warning(
                    f"Skipped {len(skipped_bases)} base currencies that failed to fetch exchange rates:\n"
                    f"{', '.join(skipped_bases)}"
                )
            else:
                logger.info("All base currencies processed successfully.")
