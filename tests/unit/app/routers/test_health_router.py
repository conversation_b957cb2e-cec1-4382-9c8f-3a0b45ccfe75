from unittest.mock import Mock

import pytest


class TestHealthRouter:
    @pytest.mark.asyncio
    async def test_healthy(self, test_client, mocker):
        mocker.patch(
            "app.routers.health.HealthService.available",
            <PERSON><PERSON>(return_value=True),
        )
        response = test_client.get("/currency/v1/health")

        assert response.status_code == 200
        assert response.json() == {"status": "OK", "services": {"available": True}}

    @pytest.mark.asyncio
    async def test_unhealth(self, test_client, mocker):
        mocker.patch(
            "app.routers.health.HealthService.available",
            <PERSON><PERSON>(return_value=False),
        )
        response = test_client.get("/currency/v1/health")

        assert response.status_code == 503
        assert response.json() == {
            "status": "UNAVAILABLE",
            "services": {"available": False},
        }
