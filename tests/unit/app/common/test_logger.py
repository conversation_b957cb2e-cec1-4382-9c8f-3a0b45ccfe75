from types import SimpleNamespace

import app.common.context_variables as global_vars
from app.common.logger import (
    FastApiJSONFormatter,
    GrpcJSONFormatter,
    configure_logger,
    logger,
)


class TestLogger:
    def test_fastapi_logger_json_formatter_format(self):
        initial_g = SimpleNamespace()
        initial_g.request_id = "test"
        initial_g.origin = "www.hotes.cloudbeds.com"
        initial_g.x_amzn_trace_id = "123456789"
        initial_g.x_property_id = "79"
        initial_g.user_id = "123"
        initial_g.user_email = "<EMAIL>"
        global_vars.request_global.set(initial_g)
        request_content = global_vars.get_request_context()
        json = FastApiJSONFormatter().json_record(
            message=None,
            extra=dict(time="custotime", x_amzn_trace_id="544545"),
            record=SimpleNamespace(
                exc_info=None,
                filename="as.json",
                funcName="a",
                levelname="WARNING",
                lineno=65,
                pathname="app/path/script.py",
            ),
        )
        assert json["x-amzn-trace-id"] == request_content.x_amzn_trace_id
        assert json["origin"] == request_content.origin

    def test_grpc_logger_json_formatter_format(self):
        initial_g = SimpleNamespace()
        initial_g.request_id = "test"
        initial_g.property_id = "79"
        initial_g.method = "/cloudbeds.currency.v1.CurrencyService/ListCurrencies"
        global_vars.request_global.set(initial_g)
        request_content = global_vars.get_request_context()
        json = GrpcJSONFormatter().json_record(
            message=None,
            extra=dict(time="custotime", x_amzn_trace_id="544545"),
            record=SimpleNamespace(
                exc_info=None,
                filename="as.json",
                funcName="a",
                levelname="WARNING",
                lineno=65,
                pathname="app/path/script.py",
            ),
        )
        assert json["request_id"] == request_content.request_id
        assert json["method"] == request_content.method
        assert json["property_id"] == request_content.property_id

    def test_fastapi_logger_json_formatter_format_no_user_data(self):
        initial_g = SimpleNamespace()
        initial_g.request_id = "test"
        initial_g.origin = "www.hotes.cloudbeds.com"
        initial_g.x_amzn_trace_id = "123456789"
        initial_g.x_property_id = "79"
        global_vars.request_global.set(initial_g)
        request_content = global_vars.get_request_context()
        json = FastApiJSONFormatter().json_record(
            message=None,
            extra=dict(time="custotime", x_amzn_trace_id="544545"),
            record=SimpleNamespace(
                exc_info=None,
                filename="as.json",
                funcName="a",
                levelname="WARNING",
                lineno=65,
                pathname="app/path/script.py",
            ),
        )
        assert json["x-amzn-trace-id"] == request_content.x_amzn_trace_id
        assert json["origin"] == request_content.origin

    def test_grpc_logger_json_formatter_format_no_metadata(self):
        initial_g = SimpleNamespace()
        initial_g.request_id = "test"
        initial_g.method = "method"
        global_vars.request_global.set(initial_g)
        request_content = global_vars.get_request_context()
        json = GrpcJSONFormatter().json_record(
            message=None,
            extra=dict(time="custotime", x_amzn_trace_id="544545"),
            record=SimpleNamespace(
                exc_info=None,
                filename="as.json",
                funcName="a",
                levelname="WARNING",
                lineno=65,
                pathname="app/path/script.py",
            ),
        )
        assert json["request_id"] == request_content.request_id
        assert json["method"] == request_content.method

    def test_configure_logger(self):
        configure_logger(FastApiJSONFormatter())
        logger.debug("Testing logger debug")
        logger.info("Testing logger info")
        logger.warning("Testing logger warning")
        logger.error("Testing logger error", exc_info="Mock Exception Info")

        configure_logger(GrpcJSONFormatter())
        logger.debug("Testing logger debug")
        logger.info("Testing logger info")
        logger.warning("Testing logger warning")
        logger.error("Testing logger error", exc_info="Mock Exception Info")

    def test_to_json(self):
        assert "{}" == FastApiJSONFormatter().to_json(SimpleNamespace(test=123))
        assert "{}" == GrpcJSONFormatter().to_json(SimpleNamespace(test=123))
