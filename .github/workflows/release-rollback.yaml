name: ⚙⚠️ Release Rollback
description: Used for rolling back a production cluster with an already built image

on:
  workflow_dispatch:
    inputs:
      image-tag:
        description: >
          Image tag (version) to rollback to (ex: v8.236.8)
        type: string
        required: true
      cluster:
        description: >
          Cluster to rollback
        type: choice
        options:
          - prod-us1
          - prod-us2
        required: true
      dry-run:
        description: >
          Dry run the rollback (simulate)
        type: boolean
        default: false

defaults:
  run:
    shell: bash

jobs:
  rollback:
    name: Rollback ${{ inputs.cluster }}
    uses: ./.github/workflows/cd-release-cluster.yaml
    secrets: inherit
    with:
        image-tag: ${{ inputs.image-tag }}
        cluster: ${{ inputs.cluster }}
        dry-run: ${{ inputs.dry-run }}
