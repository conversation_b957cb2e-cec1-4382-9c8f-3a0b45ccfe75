name: 🚧️ Deploy Stage
description: Used for deploying an already built image to a stage cluster

on:
  workflow_dispatch:
    inputs:
      image-tag:
        description: >
          Image tag (version) to deploy (ex: v8.236.8)
        type: string
        required: true
      cluster:
        description: >
          Cluster to deploy
        type: choice
        options:
          - stage-us1
          - stage-us2
        required: true
      dry-run:
        description: >
          Dry run the deployment (simulate)
        type: boolean
        default: false

defaults:
  run:
    shell: bash

jobs:
  deploy:
    name: Deploy ${{ inputs.cluster }}
    uses: ./.github/workflows/cd-release-cluster.yaml
    secrets: inherit
    with:
        image-tag: ${{ inputs.image-tag }}
        cluster: ${{ inputs.cluster }}
        dry-run: ${{ inputs.dry-run }}
