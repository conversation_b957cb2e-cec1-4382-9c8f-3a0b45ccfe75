from typing import List

from fastapi import APIRouter, Depends, Query

from app.common.dependencies import authorization
from app.models.currency import Currency
from app.services.currency import CurrencyService

router = APIRouter(
    prefix="/currencies",
    tags=["Currency"],
    responses={404: {"description": "Not found"}},
)


@router.get(
    "",
    response_model=List[Currency],
    summary="Get available currencies",
    description="Retrieves a paginated list of available currencies from the system.",
    response_description="A list of currency objects.",
)
def list_currencies(
    limit: int = Query(100),
    offset: int = Query(0),
    _: dict = Depends(authorization),
):
    return CurrencyService().list_currencies(limit=limit, offset=offset)
