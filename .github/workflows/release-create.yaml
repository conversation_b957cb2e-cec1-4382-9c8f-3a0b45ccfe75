name: ⚙🚀 Release Creation

on:
  workflow_dispatch:
    inputs:
      ref:
        description: >
          Ref for the release HEAD (e.g. main or SHA1)
        type: string
        default: main
      new-version:
        description: >
          New version (ex: v8.255.0) to be released
        type: string
        default: autodetect
      old-version:
        description: >
          Old version (ex: v8.254.0) that has been released
        type: string
        default: autodetect
      dry-run:
        description: >
          Dry run the release creation (simulate)
        type: boolean
        default: false

defaults:
  run:
    shell: bash

jobs:
  release:
    name: Release
    uses: cloudbeds/composite-actions/.github/workflows/release-create.yaml@v2
    secrets: inherit
    with:
        # GitHub App params
        app-name: CBSquadDataInsights

        # Release params
        ref: ${{ inputs.ref }}
        check-ci-success: false
        new-version: ${{ inputs.new-version }}
        old-version: ${{ inputs.old-version }}
        slack-channel: currency-service
        dry-run: ${{ inputs.dry-run }}
