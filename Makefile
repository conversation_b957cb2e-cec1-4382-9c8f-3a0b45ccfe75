ifneq (,$(wildcard ./.env))
	include .env
endif

ENV_PREFIX = stage
ENVIRONMENT = ${ENV_PREFIX}-us2
SERVICE_IMAGE_NAME = currency

.PHONY: help install-git-precommit-hook setup-env add-hosts unit-tests-run docker-build docker-delete docker-prune docker-up docker-down start stop install uninstall

ifneq (,$(wildcard ./.env))
    include .env
    export $(shell sed 's/=.*//' .env)
endif

help: ## Available commands
	@fgrep -h "##" $(MAKEFILE_LIST) | fgrep -v fgrep | sed -e 's/:.*##\s*/##/g' | awk -F'##' '{ printf "%-14s %s\n", $$1, $$2 }'

install-precommit-hook: # if host machines has python3, install and configure pre-commit
	command -v python3 >/dev/null 2>&1
	@echo Python 3 is installed
	pip install pre-commit
	pre-commit install

setup-env: ## Setup environment file
ifeq (, $(shell which envsubst))
	$(error "No envsubst in $(PATH), consider doing apt-get install gettext-base (https://command-not-found.com/envsubst)")
endif
	OPEN_EXCHANGE_RATES_APP_ID='$(shell $(MAKE) aws-ssm-get name=/mfd/${ENV_PREFIX}/open_exchange_rates/key field=OPEN_EXCHANGE_RATES_APP_ID)' \
	KAFKA_BROKER_URL='$(shell $(MAKE) aws-ssm-get name=/eks/$(ENVIRONMENT)/confluent-cloud/clusters/urls field=bootstrap | sed "s,^SASL_SSL://,,")' \
	KAFKA_BROKER_USERNAME='$(shell $(MAKE) aws-ssm-get name=/eks/$(ENVIRONMENT)/currency-service/confluent-cloud-access-keys | jq -r ".api_id")' \
	KAFKA_BROKER_PASSWORD='$(shell $(MAKE) aws-ssm-get name=/eks/$(ENVIRONMENT)/currency-service/confluent-cloud-access-keys | jq -r ".api_secret")' \
	SCHEMA_REGISTRY_URL='$(shell $(MAKE) aws-ssm-get name=/eks/$(ENVIRONMENT)/confluent-cloud/clusters/urls field=schema_registry)' \
	SCHEMA_REGISTRY_USERNAME='$(shell $(MAKE) aws-ssm-get name=/eks/$(ENVIRONMENT)/confluent-cloud/schema-registry/api-key field=id)' \
	SCHEMA_REGISTRY_PASSWORD='$(shell $(MAKE) aws-ssm-get name=/eks/$(ENVIRONMENT)/confluent-cloud/schema-registry/api-key field=secret)' \
	envsubst < .env.example > .env
	@echo "Environment file created"

aws-ssm-get:
	@aws ssm get-parameter --name '$(name)' --with-decryption --output json | jq -r '.Parameter.Value | fromjson | .$(field)'

docker.build: ## Docker build
	docker compose build

docker.delete: ## Docker delete images, volumes and its dependencies
	@docker compose down --volumes

docker.prune: ## Docker image prune
	@docker image prune --force

docker.up: ## Docker compose up
	@docker compose up database currency-api

docker.down: ## Docker compose down
	@docker compose down

start: docker.up ## Start application

stop: docker.down ## Stop application

install: setup-env docker.build install-precommit-hook migrations-upgrade ## Install application

uninstall: docker.delete docker.prune ## Uninstall application and its dependencies (images, volumes, networks)

recreate: uninstall install ## Recreate application

lint: ## Run Lint
	@uv run pre-commit run --all-files

unit-test: ## Make Unit test
	@uv run -m coverage run -m pytest -vv -s tests/unit && uv run -m coverage report && uv run coverage html

sync: ## Sync the dependencies to venv
	@uv sync

run-api: ## Run API
	uv run uvicorn app.rest_api:rest_api --reload

run-grpc: ## Run gRPC
	.venv/bin/watchmedo auto-restart --recursive --pattern="*.py" --directory="app" uv -- run app/grpc_api.py

run-exchange-job: ## Run exchange job
	uv run app/jobs/exchange_rates.py

run-exchange-job-prompt: ## Run exchange job with date prompt
	@read -p "Enter date (YYYY-MM-DD): " DATE; \
	uv run app/jobs/exchange_rates.py --date $$DATE

run-backfill:
	uv run app/jobs/backfill_exchange_rates.py --year 2025 --month 1 --day 1

run-exchange-migration-job:
	uv run app/jobs/exchange_rates_migrator.py
