name: Release Cluster Helper
description: Used for deploying an already built image to a given cluster

on:
  workflow_call:
    inputs:
      image-tag:
        description: >
          Image tag (version) to deploy (ex: v8.236.8)
        type: string
        required: true
      cluster:
        description: >
          Cluster to deploy
        type: string
        required: true
      dry-run:
        description: >
          Dry run the deployment (simulate)
        type: boolean
        default: false

defaults:
  run:
    shell: bash

jobs:
  deploy:
    name: Deploy ${{ inputs.cluster }}
    uses: cloudbeds/composite-actions/.github/workflows/deploy-cluster.yaml@v2
    secrets: inherit
    with:
        # GitHub App params
        app-name: CBSquadDataInsights

        # ArgoCD params
        image-tag: ${{ inputs.image-tag }}
        image-names: ${{ github.event.repository.name }}
        argocd-repo: argo-currency
        argocd-app-names: ${{ github.event.repository.name }}
        cluster: ${{ inputs.cluster }}

        # Workflow params
        slack-channel: currency-service
        dry-run: ${{ inputs.dry-run }}
