default_language_version:
    python: python3.12
repos:
- repo: https://github.com/astral-sh/uv-pre-commit
  rev: 0.5.26
  hooks:
      - id: uv-lock
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
    -   id: check-added-large-files
    -   id: check-toml
    -   id: check-yaml
        args:
        -   --unsafe
    -   id: end-of-file-fixer
    -   id: trailing-whitespace
-   repo: https://github.com/asottile/pyupgrade
    rev: v3.4.0
    hooks:
    -   id: pyupgrade
        args:
        - --py3-plus
        - --keep-runtime-typing
-   repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.0.270
    hooks:
    -   id: ruff
        args:
        - --fix
-   repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
    -   id: isort
        name: isort (python)
-   repo: https://github.com/psf/black
    rev: 23.1.0
    hooks:
    -   id: black
