from cloudbeds.currency.v1 import currency_pb2, currency_pb2_grpc

from app.services.currency import CurrencyService


class CurrencyServiceServicer(currency_pb2_grpc.CurrencyServiceServicer):
    async def ListCurrencies(self, request, context):
        currencies = CurrencyService().list_currencies(
            limit=request.limit or 100, offset=request.offset or 0
        )

        return currency_pb2.ListCurrenciesResponse(
            currencies=[
                currency_pb2.Currency(
                    iso_code=currency.iso_code,
                    name=currency.name,
                    scale=currency.scale,
                    symbol=currency.symbol,
                )
                for currency in currencies
            ]
        )
