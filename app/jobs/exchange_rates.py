import asyncio
import datetime
import sys

from app.common.logger import logger
from app.services.exchange_rates import ExchangeRatesService


async def main(date: str):
    logger.info(f"Running exchange rate job for: {date}")
    await ExchangeRatesService().run(date)
    logger.info("Done.")


def run():
    date = datetime.date.today().isoformat()

    if "--date" in sys.argv:
        try:
            date = sys.argv[sys.argv.index("--date") + 1]
        except IndexError:
            logger.warn("Missing value after --date")
            sys.exit(1)

    asyncio.run(main(date))


if __name__ == "__main__":
    run()
