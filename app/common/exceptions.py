import traceback
from http import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import <PERSON><PERSON>et<PERSON>

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError

from app.common.logger import logger

SERVER_ERROR_500 = {
    "code": status.HTTP_500_INTERNAL_SERVER_ERROR,
    "name": "Internal Server Error",
    "description": "The server encountered an unexpected condition that prevented it from fulfilling the request.",
    "message": "A bug report has been distributed to the Cloudbeds team.",
}
VALIDATION_ERROR_400 = {
    "code": status.HTTP_400_BAD_REQUEST,
    "name": "Bad request",
    "description": "The server encountered an unexpected condition that prevented it from fulfilling the request.",
    "message": "",
}
DIVIDE_BY_ZERO = {
    "code": status.HTTP_422_UNPROCESSABLE_ENTITY,
    "name": "Unprocessable Entity",
    "description": "The server encountered that the requested operation is performing a division by zero.",
    "message": "Division by zero is not possible, please adjust the filters.",
}
REQUEST_VALIDATION_ERROR_422 = {
    "code": status.HTTP_422_UNPROCESSABLE_ENTITY,
    "name": "Unprocessable Entity",
    "description": "The server understands the content type of the request entity but was unable to process the contained instructions.",
    "message": "",
}


def HTTP_EXCEPTION(status_code):
    return {
        "code": status_code,
        "name": HTTPStatus(status_code).phrase,
        "description": HTTPStatus(status_code).description,
        "message": "",
    }


class InvalidUsage(Exception):
    @classmethod
    def bad_request(cls, message: str = None) -> NoReturn:
        if message:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message,
            )

        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
        )

    @classmethod
    def not_found(cls, message: str = "The resource was not found") -> NoReturn:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=message,
        )

    @classmethod
    def not_authorized(
        cls, message: str = "The server couldn't authenticate"
    ) -> NoReturn:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=message)

    @classmethod
    def too_many_requests(
        cls,
        message: str = "The user has sent too many requests in a given amount of time (rate limiting)",
    ) -> NoReturn:
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=message,
        )

    @classmethod
    def server_error(cls, message: str = SERVER_ERROR_500["message"]) -> NoReturn:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=message
        )

    @classmethod
    def service_unavailable(
        cls, message: str = "There is an unspecified Server Error"
    ) -> NoReturn:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=message,
        )


def general_errorhandler(error: Exception) -> tuple[dict, int]:
    logger.error("General Service error", extra={"error": traceback.format_exc()})
    error = type("error", (object,), SERVER_ERROR_500)()
    return handle_http_exception(error)


def validation_errorhandler(error: ValidationError) -> tuple[dict, int]:
    logger.info("Validation error", extra={"error": str(error)})
    error = type(
        "error", (object,), {**VALIDATION_ERROR_400, **{"message": str(error)}}
    )()
    return handle_http_exception(error)


def request_validation_errorhandler(error: RequestValidationError) -> tuple[dict, int]:
    logger.info("Request validation error", extra={"error": str(error)})
    error = type(
        "error", (object,), {**REQUEST_VALIDATION_ERROR_422, **{"message": str(error)}}
    )()
    return handle_http_exception(error)


def http_exception_handler(error: HTTPException) -> tuple[dict, int]:
    logger.info(
        str(error.detail),
        extra={"error": str(error), "status_code": error.status_code},
    )
    error = type(
        "error",
        (object,),
        {**HTTP_EXCEPTION(error.status_code), **{"message": str(error.detail)}},
    )()
    return handle_http_exception(error)


def handle_http_exception(error: object) -> tuple[dict, int]:
    """Return a JSON response containing a description of the error

    - `message` (``str``): a comment
    - `errors` (``dict``): errors, typically validation errors in
        parameters and request body
    """
    # Get additional info passed as kwargs when calling abort
    # data may not exist if HTTPException was raised without webargs abort
    data = getattr(error, "data", None)

    # Send the custom message, otherwise select suitable error message
    if hasattr(error, "message"):
        error_message = error.message
    elif data:
        if "message" in data:
            error_message = data["message"]
        if "errors" in data:
            error_message = data["errors"]
        elif "messages" in data:
            error_message = data["messages"]
    else:
        error_message = HTTPStatus(error.code).description

    payload = dict(
        error=dict(
            code=error.code,
            status=error.name,
            description=error.description,
            message=error_message,
        )
    )

    return payload, error.code
