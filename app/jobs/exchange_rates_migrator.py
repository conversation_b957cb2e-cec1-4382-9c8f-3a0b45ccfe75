import os
from confluent_kafka import Consumer, Producer, KafkaException
from confluent_kafka.serialization import SerializationContext, MessageField
from confluent_kafka.schema_registry import SchemaRegistryClient
from confluent_kafka.schema_registry.avro import AvroDeserializer, AvroSerializer

MAX_MESSAGES = 10_000_000
TOPIC_NAME = os.getenv("KAFKA_TOPIC_EXCHANGE_RATES", "service.currency.exchange_rates")

# Stage (source) config
stage_consumer_config = {
    "bootstrap.servers": os.getenv("STAGE_KAFKA_BROKER_URL"),
    "security.protocol": "SASL_SSL",
    "sasl.mechanism": "PLAIN",
    "sasl.username": os.getenv("STAGE_KAFKA_BROKER_USERNAME"),
    "sasl.password": os.getenv("STAGE_KAFKA_BROKER_PASSWORD"),
    "group.id": f"exchange-rates-migration-aar",
    "auto.offset.reset": "earliest",
    "enable.auto.commit": True,
}

stage_schema_registry = SchemaRegistryClient({
    "url": os.getenv("STAGE_SCHEMA_REGISTRY_URL"),
    "basic.auth.user.info": f"{os.getenv('STAGE_SCHEMA_REGISTRY_USERNAME')}:{os.getenv('STAGE_SCHEMA_REGISTRY_PASSWORD')}",
})

# Prod (destination) config
prod_producer_config = {
    "bootstrap.servers": os.getenv("PROD_KAFKA_BROKER_URL"),
    "security.protocol": "SASL_SSL",
    "sasl.mechanism": "PLAIN",
    "sasl.username": os.getenv("PROD_KAFKA_BROKER_USERNAME"),
    "sasl.password": os.getenv("PROD_KAFKA_BROKER_PASSWORD"),
}

prod_schema_registry = SchemaRegistryClient({
    "url": os.getenv("PROD_SCHEMA_REGISTRY_URL"),
    "basic.auth.user.info": f"{os.getenv('PROD_SCHEMA_REGISTRY_USERNAME')}:{os.getenv('PROD_SCHEMA_REGISTRY_PASSWORD')}",
})


def delivery_report(err, msg):
    if err:
        print(f"[Delivery failed] {err}")
    else:
        print(
            f"[Delivered] Key={msg.key()} to {msg.topic()} [{msg.partition()}] @ offset {msg.offset()}"
        )


def main():
    print(f"🚀 Starting migration from stage → prod on topic: {TOPIC_NAME}")
    consumer = Consumer(stage_consumer_config)
    consumer.subscribe([TOPIC_NAME])

    key_schema = stage_schema_registry.get_latest_version(f"{TOPIC_NAME}-key").schema.schema_str
    value_schema = stage_schema_registry.get_latest_version(f"{TOPIC_NAME}-value").schema.schema_str

    key_deserializer = AvroDeserializer(stage_schema_registry, key_schema)
    value_deserializer = AvroDeserializer(stage_schema_registry, value_schema)

    key_serializer = AvroSerializer(prod_schema_registry, key_schema, lambda x, _: x)
    value_serializer = AvroSerializer(prod_schema_registry, value_schema, lambda x, _: x)

    producer = Producer(prod_producer_config)

    total = 0
    empty_count = 0

    try:
        while total < MAX_MESSAGES:
            msg = consumer.poll(5.0)
            if msg is None:
                empty_count += 1
                if empty_count > 3:
                    print("⏹️ No more messages to consume.")
                    break
                print("⏳ No message received yet...")
                continue

            if msg.error():
                raise KafkaException(msg.error())

            key = key_deserializer(msg.key(), SerializationContext(TOPIC_NAME, MessageField.KEY))
            value = value_deserializer(msg.value(), SerializationContext(TOPIC_NAME, MessageField.VALUE))

            # print(f"🔑 {key} — migrating message {total + 1}")

            try:
                producer.produce(
                    topic=TOPIC_NAME,
                    key=key_serializer(key, SerializationContext(TOPIC_NAME, MessageField.KEY)),
                    value=value_serializer(value, SerializationContext(TOPIC_NAME, MessageField.VALUE)),
                    on_delivery=delivery_report,
                )
                total += 1
                producer.poll(0.0)

            except Exception as e:
                print(f"❌ Produce error: {e}")

            if total >= MAX_MESSAGES:
                print(f"🛑 Reached max limit of {MAX_MESSAGES} messages for this run.")
                break

    finally:
        print(f"🌀 Flushing {total} messages...")
        producer.flush(10)
        consumer.close()
        print(f"✅ Migration complete. {total} messages migrated.")

if __name__ == "__main__":
    main()
