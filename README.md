# Currency Service

The **Currency Service** is the **source of truth for currencies** within the Cloudbeds platform. It provides a reliable interface for both querying supported currencies and distributing **daily exchange rates** across the platform via Kafka. It is implemented in **FastAPI** and **gRPC**, running in **Kubernetes**, and interacts with Confluent Cloud services.

---

## Features

- **📡 REST & gRPC APIs**: Exposes endpoints to query currency metadata and rate information.
- **🔁 Daily Exchange Rates Job**: Publishes exchange rates between supported currencies to a Kafka topic.

---

## Architecture Overview

- **FastAPI** serves REST endpoints at `/currency/v1/currencies`.
- **gRPC** provides a performant alternative interface for internal service communication.
- **Exchange Rates Job** fetches rates from external providers (e.g., Open Exchange Rates) and publishes to Kafka.
- **Kafka** is used for disseminating exchange rate information.

---

## Quick Start

### 1. Install Dependencies and Set Up Environment

```bash
make install
```

This will:
- Generate a `.env` file from `.env.example`, substituting secrets from AWS SSM.
- Build Docker containers.
- Install pre-commit hooks.
- Apply any required DB migrations.

---

### 2. Run Unit Tests

```bash
make unit-test
```

This runs all unit tests with `pytest`, generates a coverage report, and produces an HTML coverage summary.

---

### 3. Running the Service Locally

#### Start REST API and database:
```bash
make start
```

#### Stop services:
```bash
make stop
```

#### Run REST API only:
```bash
make run-api
```

#### Run gRPC server:
```bash
make run-grpc
```

---

### 4. Run Exchange Rates Job

This job fetches daily exchange rates and publishes them to a Kafka topic using AVRO serialization.

```bash
make run-exchange-job
```

Or manually prompt for a specific date:

```bash
make run-exchange-job-prompt
```

---

## Development Workflow

### Linting
```bash
make lint
```

### Sync Dependencies to Virtual Environment
```bash
make sync
```

### Recreate the Full Setup (Clean Install)
```bash
make recreate
```

---

## Environment Variables

Environment variables are automatically generated from `.env.example` using secrets stored in AWS SSM. Example of keys used:

- `/mfd/stage/open_exchange_rates/key`
- `/eks/stage-us2/currency-service/confluent-cloud-access-keys`
- `/eks/stage-us2/confluent-cloud/schema-registry/api-key`

Ensure your AWS CLI is authenticated and you have access to the appropriate parameters.

---

## Docker Commands

- `make docker.build` – Build the service container.
- `make docker.up` – Spin up DB and API locally.
- `make docker.down` – Shut down running services.
- `make docker.delete` – Remove containers, networks, and volumes.
- `make docker.prune` – Remove unused images.

---

## API Documentation

After running the API, you can access the Swagger UI at:

```
http://localhost:8000/currency
```
