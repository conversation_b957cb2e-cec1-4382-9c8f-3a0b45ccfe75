name: 🚀 Deploy Release

on:
  push:
    tags:
      - "v[0-9]+.[0-9]+.[0-9]+"

jobs:
  docker-images:
    name: Re-tag image
    runs-on: x1-core
    permissions:
      id-token: write
      contents: read
    outputs:
      image-tag: ${{ steps.docker-images.outputs.image_tag }}
    steps:
      - name: Re-tag image application image
        id: docker-images
        uses: cloudbeds/composite-actions/docker/crane-re-tag/aws-ecr@v2

  deploy-prod-us2:
    name: Deploy prod-us2
    needs: docker-images
    uses: ./.github/workflows/cd-deploy-cluster.yaml
    secrets: inherit
    with:
      cluster: prod-us2
      image-tag: ${{ needs.docker-images.outputs.image-tag }}

  deploy-prod-us1:
    name: Deploy prod-us1
    needs:
      - docker-images
      - deploy-prod-us2
    uses: ./.github/workflows/cd-deploy-cluster.yaml
    secrets: inherit
    with:
      cluster: prod-us1
      image-tag: ${{ needs.docker-images.outputs.image-tag }}
