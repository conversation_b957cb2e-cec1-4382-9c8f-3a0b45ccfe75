from app.data.currency import CURRENCIES
from app.models.currency import Currency


class TestCurrencyService:
    def test_list_currencies_default_limit(self, currency_service):
        result = currency_service.list_currencies()
        assert isinstance(result, list)
        assert all(isinstance(c, Currency) for c in result)
        assert len(result) <= 100

    def test_list_currencies_with_limit(self, currency_service):
        result = currency_service.list_currencies(limit=5)
        assert len(result) == 5
        assert result == CURRENCIES[:5]

    def test_list_currencies_with_offset(self, currency_service):
        result = currency_service.list_currencies(limit=3, offset=2)
        assert result == CURRENCIES[2:5]

    def test_list_currencies_beyond_range(self, currency_service):
        result = currency_service.list_currencies(offset=99999)
        assert result == []

    def test_list_currencies_negative_values(self, currency_service):
        result = currency_service.list_currencies(limit=-1, offset=-1)
        assert result == CURRENCIES[-1:-2]
