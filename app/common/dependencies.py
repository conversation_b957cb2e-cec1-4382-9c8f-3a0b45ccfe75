from http import HTTPStatus

from fastapi import Depends, Head<PERSON>, HTTPException
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from app.common.constants import X_PROPERTY_ID
from app.services.token import TokenService


def authorization(
    propertyId: int = Header(alias=X_PROPERTY_ID, description="Property ID"),
    token: HTTPAuthorizationCredentials = Depends(
        HTTPBearer(
            bearerFormat="JWT",
            scheme_name="Authorization",
            description="Token",
            auto_error=False,
        )
    ),
):
    if not propertyId:
        raise HTTPException(
            status_code=HTTPStatus.BAD_REQUEST,
            detail=f"{X_PROPERTY_ID} header is missing",
        )

    if not token:
        raise HTTPException(
            status_code=HTTPStatus.UNAUTHORIZED, detail="Access token is missing"
        )

    token_service = TokenService(token.credentials)
    if token_service.is_super_admin():
        return dict(property_id=propertyId, token=token)

    if propertyId not in token_service.get_property_ids():
        raise HTTPException(
            status_code=HTTPStatus.UNAUTHORIZED,
            detail="Property ID provided is not authorized via access token",
        )

    return dict(property_id=propertyId, token=token)
