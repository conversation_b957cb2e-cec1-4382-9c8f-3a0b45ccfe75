name: Cluster Deployment

on:
  workflow_call:
    inputs:
      cluster:
        description: Cluster to deploy (e.g. stage-us2, prod-us1)
        type: string
        required: true
      image-tag:
        description: Docker image tag
        type: string
        required: true
      dry-run:
        description: Dry run the deployment (simulate)
        type: boolean
        default: false

# We run a single deployment per cluster at a time, next workflows will politely wait
# for the current workflow running to finish.
concurrency:
  group: deployment / ${{ inputs.cluster }}
  cancel-in-progress: false

defaults:
  run:
    shell: bash

jobs:
  deploy:
    name: ArgoCD ${{ inputs.cluster }}
    environment: ${{ inputs.cluster }}
    runs-on: x1-core
    permissions:
      id-token: write
      contents: read
    timeout-minutes: 15
    steps:
      - name: Get GH app token
        id: gh-app-token
        uses: cloudbeds/composite-actions/gh-app-token@v2
        with:
          repositories: >-
            ["argocd-currency"]
          app_name: CBSquadDataInsights

      - name: ArgoCD ${{ inputs.cluster }}
        uses: cloudbeds/composite-actions/trigger-argocd@v2
        with:
          repo: argocd-currency
          app-names: ${{ github.event.repository.name }}
          github-token: ${{ steps.gh-app-token.outputs.github-token }}
          cluster: ${{ inputs.cluster }}
          image-tag: ${{ inputs.image-tag }}
          dry-run: ${{ inputs.dry-run }}
