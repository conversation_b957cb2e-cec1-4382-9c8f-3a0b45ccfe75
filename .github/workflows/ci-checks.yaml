name: <PERSON><PERSON> Checks

on:
  workflow_call:
    inputs:
      push:
        description: Indicates if we need to push the docker image
        default: false
        type: boolean
    outputs:
      image-tag:
        description: Docker image tag
        value: ${{ jobs.build.outputs.image-tag }}

permissions:
  id-token: write
  contents: read

jobs:
  run-code-lint:
    name: Code Lint 🧹
    runs-on: default
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4

      - name: <PERSON> Lint
        uses: ./.github/actions/code-lint

  run-unit-tests:
    name: Unit Testing 🧪
    runs-on: default
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4

      - name: Run Unit Tests
        uses: ./.github/actions/unit-tests

  build:
    name: Build and push image 🧱
    runs-on: default
    outputs:
      image-tag: ${{ steps.build.outputs.image_tag }}
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4

      - name: Get GH app token
        id: gh-app-token
        uses: cloudbeds/composite-actions/gh-app-token@v2

      # Application Image
      - name: Build and push application image
        id: build
        uses: cloudbeds/composite-actions/docker/build-push/aws-ecr@v2
        with:
          push: ${{ inputs.push}}
          docker_build_target: prod
          docker_build_args: |
            GITHUB_ACCESS_TOKEN=${{ steps.gh-app-token.outputs.github-token }}
