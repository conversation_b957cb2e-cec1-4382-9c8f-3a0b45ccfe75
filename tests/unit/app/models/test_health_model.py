import pytest
from pydantic import ValidationError

from app.models.health import Health


class TestHealthCheckModel:
    def test_health_default_status(self):
        health = Health(services={"available": True})

        assert health.status == "OK"
        assert health.services["available"] is True

    def test_health_custom_status(self):
        health = Health(status="UNAVAILABLE", services={"available": False})

        assert health.status == "UNAVAILABLE"
        assert health.services == {"available": False}

    def test_health_missing_services(self):
        with pytest.raises(ValidationError) as exc_info:
            Health()
        assert "Field required" in str(exc_info.value)

    def test_health_invalid_services_type(self):
        with pytest.raises(ValidationError) as exc_info:
            Health(services="not-a-dict")
        assert "Input should be a valid dictionary" in str(exc_info.value)
