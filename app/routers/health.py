from fastapi import APIRouter, Response
from fastapi import status as http_status_code

from app.models.health import Health
from app.services.health import HealthService

router = APIRouter(
    prefix="/health",
    tags=["Health"],
    responses={404: {"description": "Not found"}},
)


@router.get(
    "",
    summary="Perform a Health Check",
    response_description="Return HTTP Status Code 200 (OK)",
    status_code=http_status_code.HTTP_200_OK,
    response_model=Health,
)
async def get_health(response: Response) -> Health:
    """
    ## Perform a Health Check
    Endpoint to perform a healthcheck on. This endpoint can primarily be used Docker
    to ensure a robust container orchestration and management is in place. Other
    services which rely on proper functioning of the API service will not deploy if this
    endpoint returns any other HTTP status code except 200 (OK).
    Returns:
        Health: Returns a JSON response with the health status
    """
    health_service = HealthService()
    services = dict(available=health_service.available())

    status = "OK"
    for _key, healthy in services.items():
        if healthy is False:
            status = "UNAVAILABLE"
            response.status_code = http_status_code.HTTP_503_SERVICE_UNAVAILABLE

    return Health(status=status, services=services)
