name:  🧪 Merge into main branch

on:
  push:
    branches:
      - main

permissions:
  id-token: write
  contents: read

jobs:
  ci-checks:
    name: C<PERSON> checks
    uses: ./.github/workflows/ci-checks.yaml
    secrets: inherit
    with:
      push: true

  deploy-stage-us2:
    name: Deploy stage-us2
    needs: ci-checks
    uses: ./.github/workflows/cd-deploy-cluster.yaml
    secrets: inherit
    with:
      cluster: stage-us2
      image-tag: ${{ needs.ci-checks.outputs.image-tag }}

  deploy-stage-us1:
    name: Deploy stage-us1
    needs:
      - ci-checks
      - deploy-stage-us2
    uses: ./.github/workflows/cd-deploy-cluster.yaml
    secrets: inherit
    with:
      cluster: stage-us1
      image-tag: ${{ needs.ci-checks.outputs.image-tag }}
