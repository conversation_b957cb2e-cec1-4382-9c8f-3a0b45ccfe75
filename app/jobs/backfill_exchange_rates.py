import asyncio
import argparse
from datetime import date, timedelta

from app.services.exchange_rates import ExchangeRatesService
from app.common.logger import logger


def daterange(start: date, end: date):
    current = start

    while current <= end:
        yield current
        current += timedelta(days=1)


async def run_year(year: int, month: int = 1, day: int = 1):
    start = date(year, 1, 1)
    end = date(year, 12, 31)

    start = date(year, month, day) if month and day else start

    print(f"📦 Starting backfill for year {year} ({start} → {end})")
    service = ExchangeRatesService()

    failed_dates = []

    for current_date in daterange(start, end):
        date_str = current_date.strftime("%Y-%m-%d")
        print(f"📅 Processing: {date_str}")

        try:
            await service.run(date_str)
        except Exception as e:
            logger.error(f"❌ Failed for {date_str}: {e}")
            failed_dates.append(date_str)

        await asyncio.sleep(0.25)  # 250ms pause to avoid flooding

    if failed_dates:
        logger.warning(f"⚠️ {len(failed_dates)} days failed during {year}:")
        for d in failed_dates:
            logger.warning(f" - {d}")
    else:
        print(f"✅ Completed year {year} with no errors.")


def parse_args():
    parser = argparse.ArgumentParser(description="Backfill exchange rates for a year.")
    parser.add_argument("--year", type=int, required=True, help="Year to backfill (e.g. 2015)")
    parser.add_argument("--month", type=int, help="Month to backfill (1-12)")
    parser.add_argument("--day", type=int, help="Day to backfill (1-31)")
    return parser.parse_args()


def main():
    args = parse_args()
    asyncio.run(run_year(args.year, args.month, args.day))


if __name__ == "__main__":
    main()
